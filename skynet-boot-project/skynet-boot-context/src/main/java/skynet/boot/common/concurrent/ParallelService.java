package skynet.boot.common.concurrent;


import com.alibaba.ttl.TtlCallable;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 并行计算工具类
 * <p>
 * 采用了TTL 跨线程上下文传递
 * <pre>
 * {@code
 *     public static void main(String[] args) throws Exception {
 *
 *         List<Integer> taskList = Arrays.asList(1, 2, 3, 4, 5, 6, 7, 8, 9, 10);
 *
 *         ParallelService<Integer, Integer> parallelService = new ParallelService<>(10);
 *         parallelService.submit(taskList, input -> (int) Math.pow(input, 2));
 *
 *         // 一直等待结束
 *         List<Integer> resultList = parallelService.getResult();
 *         System.out.println(resultList.size());
 *
 *         // OR 超时等待
 *         List<Integer> resultList2 = parallelService.getResult(1000, TimeUnit.SECONDS);
 *         System.out.println(resultList2.size());
 *
 *         parallelService.close();
 *     }
 * }
 * </pre>
 *
 * @param <I> 输入参数类型
 * @param <O> 计算输出类型
 * <AUTHOR> [2016年7月3日上午11:38:57]
 */
@Slf4j
public class ParallelService<I, O> implements AutoCloseable {

    private static final AtomicInteger index = new AtomicInteger(0);
    private final ExecutorService executor;
    private List<Future<O>> futureList;
    private CompletionService<O> completionService;
    private boolean isInnerExecutor;

    /**
     * 并行计算
     *
     * <pre>
     * 并发线程数 缺省 CPU 核数
     * </pre>
     */
    public ParallelService() {
        this(Runtime.getRuntime().availableProcessors());
    }

    /**
     * 并行计算
     *
     * @param nThreads 并发线程数
     */
    public ParallelService(int nThreads) {
        this(Executors.newFixedThreadPool(nThreads,
                Thread.ofVirtual().name("skynet-ParallelService-", index.getAndIncrement()).factory()));
        this.isInnerExecutor = true;
    }

    public ParallelService(ExecutorService executor) {
        this.executor = executor;
    }


    public void submit(I[] taskList, final ParallelFunc<I, O> func) {
        this.submit(Arrays.asList(taskList), func);
    }

    /**
     * 提交计算任务
     *
     * @param taskList 任务列表
     * @param func     计算函数
     */
    public void submit(List<I> taskList, final ParallelFunc<I, O> func) {
        submit(taskList, func, false);
    }

    /**
     * 提交计算任务
     *
     * @param taskList 任务列表
     * @param func     计算函数
     * @param withTTL  是否跨进行线程上下文传递 （ 依赖 com.alibaba ：transmittable-thread-local 组件）
     */
    public void submit(List<I> taskList, final ParallelFunc<I, O> func, boolean withTTL) {

        this.completionService = new ExecutorCompletionService<>(executor);
        this.futureList = new ArrayList<>(taskList.size());

        for (final I task : taskList) {
            Callable<O> callable = () -> func.call(task);
            Future<O> f = completionService.submit(withTTL ? TtlCallable.get(callable) : callable);
            this.futureList.add(f);
        }
    }

    /**
     * 获取计算结果列表
     *
     * <pre>
     * 如果计算结果为null，将不返回, 永不超时
     * </pre>
     *
     * @return 计算结果列表
     */
    public List<O> getResult() {
        return this.getResult(Long.MAX_VALUE, TimeUnit.MILLISECONDS);
    }

    /**
     * 获取计算结果列表
     *
     * <pre>
     * 如果计算结果为null，将不返回,
     * </pre>
     *
     * @param timeout 超时时间
     * @param unit    超时时间单位
     * @return 计算结果列表
     */
    public List<O> getResult(long timeout, TimeUnit unit) {
        List<O> resultList = new ArrayList<>(futureList.size());
        for (int i = 0; i < futureList.size(); i++) {
            try {
                Future<O> responseFuture = completionService.poll(timeout, unit);
                if (responseFuture != null) {
                    O response = responseFuture.get();
                    if (response != null) {
                        resultList.add(response);
                    }
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            } catch (ExecutionException e) {
                log.error("ExecutionException", e);
            } catch (Throwable e) {
                log.error("responseFuture error", e);
            }
        }

        // 如果超时则终止所有任务（注意cancel只是调用子线程的interrupt方法，至于能不能中断得看子线程是否支持）
        // 因为对于已经完成的任务调用Future.cancel不起效，所以不需要排除那些已经完成的任务
        for (Future<O> future : futureList) {
            future.cancel(true);
        }

        return resultList;
    }

    @Override
    public void close() throws Exception {
        if (isInnerExecutor) {
            log.debug("close inner executor");
            this.executor.shutdown();
        }
    }

//    public static void main(String[] args) throws Exception {
//
//        List<Integer> taskList = Arrays.asList(1, 2, 3, 4, 5, 6, 7, 8, 9, 10);
//
//        ParallelService<Integer, Integer> parallelService = new ParallelService<>(10);
//        parallelService.submit(taskList, input -> (int) Math.pow(input, 2));
//
//        // 一直等待结束
//        List<Integer> resultList = parallelService.getResult();
//        System.out.println(resultList.size());
//
//        // OR 超时等待
//        List<Integer> resultList2 = parallelService.getResult(1000, TimeUnit.SECONDS);
//        System.out.println(resultList2.size());
//
//        parallelService.close();
//    }

}