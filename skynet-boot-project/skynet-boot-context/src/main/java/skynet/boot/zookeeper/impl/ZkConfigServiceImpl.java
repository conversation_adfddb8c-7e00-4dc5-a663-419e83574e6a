package skynet.boot.zookeeper.impl;

import com.alibaba.fastjson2.JSON;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.curator.RetryPolicy;
import org.apache.curator.framework.CuratorFramework;
import org.apache.curator.framework.CuratorFrameworkFactory;
import org.apache.curator.framework.recipes.cache.ChildData;
import org.apache.curator.framework.recipes.cache.TreeCache;
import org.apache.curator.framework.recipes.cache.TreeCacheEvent;
import org.apache.curator.retry.RetryNTimes;
import org.apache.zookeeper.CreateMode;
import org.apache.zookeeper.ZooDefs;
import org.apache.zookeeper.data.ACL;
import org.apache.zookeeper.data.Id;
import org.apache.zookeeper.data.Stat;
import org.apache.zookeeper.server.auth.DigestAuthenticationProvider;
import org.springframework.util.StringUtils;
import skynet.boot.zookeeper.SkynetZkProperties;
import skynet.boot.zookeeper.ZkConfigService;

import java.io.File;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

/**
 * IZkConfigService 实现类
 *
 * <AUTHOR> 2014年4月1日 上午11:29:58
 */
@Slf4j
public class ZkConfigServiceImpl extends Observable implements ZkConfigService {

    public final static String ZK_SYSTEM_NODE = "zookeeper";
    private final static Charset CHARSET_UTF8 = StandardCharsets.UTF_8;
    private final Cache<String, String> stringCache;
    private final Cache<String, List<String>> listCache;
    private final CuratorFramework client;
    private final List<ACL> aclList;
    private final SkynetZkProperties skynetZkProperties;

    private static String removeLineBreak(String data) {
        return data.replaceAll("\r", "").replaceAll("\n", "\\\\n");
    }

    public ZkConfigServiceImpl(SkynetZkProperties skynetZkProperties) throws Exception {
        log.debug("build ZkConfigServiceImpl ..");
        String connectString = skynetZkProperties.getServerList();
        if (!StringUtils.hasText(connectString)) {
            throw new Exception(String.format("Not config [%s]", SkynetZkProperties.SKYNET_ZOOKEEPER_SERVER_LIST));
        }

        log.info("-- skynet zookeeper setting -------------");
        log.info("zookeeper_servers:\t{}", connectString);
        log.info("zookeeper_sessionTimeout:\t{} ms", skynetZkProperties.getSessionTimeout());
        log.info("zookeeper_connectionTimeout:\t{} ms", skynetZkProperties.getConnectionTimeout());
        log.info("zookeeper_cache_ttl:\t{} ms", skynetZkProperties.getCacheTtl());

        //缓存 解决 上层服务一次请求多次访问的性能
        this.stringCache = CacheBuilder.newBuilder().maximumSize(2048).expireAfterWrite(skynetZkProperties.getCacheTtl(), TimeUnit.MILLISECONDS).concurrencyLevel(10).recordStats().build();
        this.listCache = CacheBuilder.newBuilder().maximumSize(512).expireAfterWrite(skynetZkProperties.getCacheTtl(), TimeUnit.MILLISECONDS).concurrencyLevel(10).recordStats().build();

        RetryPolicy retryPolicy = new RetryNTimes(3, skynetZkProperties.getConnectionTimeout());
        // 自定义权限列表
        this.aclList = new ArrayList<>();
        CuratorFrameworkFactory.Builder builder = CuratorFrameworkFactory.builder();
        if (skynetZkProperties.getAclUser() != null) {
            SkynetZkProperties.ZkUser zkUser = skynetZkProperties.getAclUser();
            if (StringUtils.hasText(zkUser.getUsername())
                    && StringUtils.hasText(zkUser.getPassword())) {
                String userId = String.format("%s:%s", zkUser.getUsername(), zkUser.getPassword());
                builder = builder.authorization("digest", userId.getBytes());
                Id user1 = new Id("digest", DigestAuthenticationProvider.generateDigest(userId));
                aclList.add(new ACL(ZooDefs.Perms.ALL, user1));
            }
        }
        this.client = builder.connectString(connectString)
                .sessionTimeoutMs(skynetZkProperties.getSessionTimeout()).retryPolicy(retryPolicy)
                //.namespace("workspace")
                .build();
        this.client.start();
        this.skynetZkProperties = skynetZkProperties;
        log.info("Zookeeper CuratorFramework state = {}", client.getState());
        log.info("-----------------------------------------");
    }


    @Override
    public SkynetZkProperties getZkProperties() {
        return skynetZkProperties;
    }

    @Override
    @SneakyThrows
    public void deleteNode(String path) {
        //initZkClient();
        if (StringUtils.hasText(path) && exists(path)) {
            log.debug("deleteNode path={}", path);
            invalidateCache(path);
            this.client.delete().deletingChildrenIfNeeded().forPath(path);
        }
    }

    private void invalidateCache(String path) {
        if (StringUtils.hasText(path)) {
            this.stringCache.invalidate(path);
            this.listCache.invalidate(path);
        }
    }

    @Override
    @SneakyThrows
    public boolean exists(String path) {
        //initZkClient();
        log.debug("exists path={}", path);

        if (!StringUtils.hasText(path) || !path.startsWith("/")) {
            log.error("check the path[{}] exist.", path);
            return false;
        }
        Stat stat = this.client.checkExists().forPath(path);
        return stat != null;
    }

    @Override
    public List<String> getChildren(String path) {
        List<String> objList = listCache.getIfPresent(path);
        if (objList == null) {
            if (exists(path)) {
                log.debug("getChildren path={}", path);
                try {
                    objList = this.client.getChildren().forPath(path);
                } catch (Throwable e) {
                    log.warn("getChildren error：{}", e.toString());
                }
            }
            objList = (objList == null) ? new ArrayList<>(0) : objList;
            log.trace("getChildren path={};list={}", path, objList);
            listCache.put(path, objList);
        } else {
            log.debug("Get Children From Cache.[path={}]", path);
        }
        return objList;
    }

    @Override
    public Map<String, String> getChildrenWithData(String path) {
        //initZkClient();
        log.debug("getChildrenWithData path={}", path);
        return this.getChildrenWithData(path, getChildren(path));
    }

    @Override
    public Map<String, String> getChildrenWithData(String parentPath, List<String> currentChildren) {
        log.debug("getChildrenWithData parentPath={}", parentPath);
        Map<String, String> map = new TreeMap<>();
        if (null == currentChildren) {
            return map;
        }
        //initZkClient();
        for (String childName : currentChildren) {
            String childPath = this.getFullPath(parentPath, childName);
            String data = this.getData(childPath);
            map.put(childPath, null == data ? "" : data);
        }
        if (log.isTraceEnabled()) {
            log.trace("getChildrenWithData parentPath={};map={}", parentPath, JSON.toJSONString(map));
        }
        return map;
    }

    @Override
    public Map<String, String> getChildrenWithData(String path, final Observer childrenObserver) {
        //initZkClient();
        log.debug("getChildrenWithData path={}", path);

        // 读取数据
        Map<String, String> map = getChildrenWithData(path);

        if (childrenObserver != null) {
            // 订阅子节点变化通知
            watchChildrenObserver(path, childrenObserver);
        }
        return map;
    }

    @Override
    @SneakyThrows
    public String getData(String path) {
        String data = stringCache.getIfPresent(path);
        if (data == null) {
            //initZkClient();
            log.debug("getData path={}", path);
            if (exists(path)) {
                byte[] bytes = client.getData().forPath(path);
                data = (null == bytes) ? "" : new String(bytes, CHARSET_UTF8).trim();
                log.trace("getData path={};data={}", path, data);
                stringCache.put(path, data);
            } else {
                log.debug("getData the path={}; no exists.", path);
                return null;
            }
        } else {
            log.debug("Get Data From Cache.[path={}]", path);
        }
        return data;
    }

    @Override
    public String getData(String path, final Observer dataObserver) {
        //initZkClient();
        log.debug("getData with Observer path={}", path);

        String data = getData(path);
        // 订阅数据变化通知
        if (dataObserver != null) {
            watchData(path, dataObserver);
        }

        return StringUtils.hasText(data) ? data.trim() : "";
    }

    private String getFullPath(String parentPath, String name) {
        return parentPath + (parentPath.endsWith("/") ? "" : "/") + name;
    }

    @Override
    public void putNode(String path, String data) {
        this.putNode(path, (null == data ? "" : data.trim()).getBytes(CHARSET_UTF8));
    }

    @Override
    public void putNode(String path, String name, String data) {
        this.putNode(getFullPath(path, name), data);
    }

    @Override
    @SneakyThrows
    public void putNode(String path, byte[] data) {
        //initZkClient();
        invalidateCache(path);
        log.debug("putNode path={}", path);

        if (log.isTraceEnabled()) {
            log.debug("putNode path={};data={}", path, new String(data, CHARSET_UTF8));
        }
        if (!this.aclList.isEmpty()) {
            this.client.create().orSetData().creatingParentsIfNeeded().withACL(this.aclList).forPath(path, data);
        } else {
            this.client.create().orSetData().creatingParentsIfNeeded().forPath(path, data);
        }
    }

    @Override
    public void putNode(String path, String name, byte[] data) {
        this.putNode(getFullPath(path, name), data);
    }

    /**
     * 创建一个 EPHEMERAL_SEQUENTIAL 类型的节点（临时有序节点，session断开即消失）
     */
    @Override
    @SneakyThrows
    public String putSequenceNode(String path, String prefix, String data) {
        //initZkClient();
        log.debug("putSequenceNode path={};prefix={}", path, prefix);
        log.trace("putSequenceNode data={}", data);
        byte[] bytes = (null == data ? "" : data).getBytes(CHARSET_UTF8);
        path = getFullPath(path, prefix);
        invalidateCache(path);

        return this.client.create().orSetData().creatingParentsIfNeeded().withMode(CreateMode.EPHEMERAL_SEQUENTIAL).forPath(path, bytes);
    }

    @Override
    @SneakyThrows
    public String putEphemeralNode(String path, String data) {
        //initZkClient();
        log.debug("putEphemeralNode path={}", path);
        log.trace("putEphemeralNode data={}", data);
        invalidateCache(path);
        byte[] bytes = (null == data ? "" : data).getBytes(CHARSET_UTF8);
        return this.client.create().orSetData().creatingParentsIfNeeded().withMode(CreateMode.EPHEMERAL).forPath(path, bytes);
    }

    private final Map<TreeCacheParam, TreeCache> watchChildrenObserverTreeCache = new ConcurrentHashMap<>();

    @Override
    @SneakyThrows
    public void watchChildrenObserver(String parentPath, final Observer childrenObserver) {

        //initZkClient();
        log.debug("watchChildrenObserver parentPath={}", parentPath);

        TreeCacheParam treeCacheParam = new TreeCacheParam(parentPath, childrenObserver);
        synchronized (watchChildrenObserverTreeCache) {
            if (!watchChildrenObserverTreeCache.containsKey(treeCacheParam)) {
                final Observable _this = this;
                log.debug("A parent path[{}] watcher is creating...", parentPath);
                TreeCache treeCache = new TreeCache(client, parentPath);
                //设置监听器和处理过程
                treeCache.getListenable().addListener((client, event) -> {
                    ChildData data = event.getData();
                    if (data != null) {
                        switch (event.getType()) {
                            case NODE_ADDED:
                            case NODE_REMOVED:
                            case NODE_UPDATED:
                                if (log.isDebugEnabled()) {
                                    log.debug("NODE_{}= {} Data= {}", event.getType(), data.getPath(), new String(data.getData()));
                                }
                                childrenObserver.update(_this, getChildrenWithData(parentPath));
                                break;
                            default:
                                break;
                        }
                    } else if (event.getType() != TreeCacheEvent.Type.INITIALIZED) {
                        log.warn("data is null = {}", event.getType());
                    }
                });
                //开始监听
                treeCache.start();
                watchChildrenObserverTreeCache.put(treeCacheParam, treeCache);
            } else {
                log.debug("The parentPath={};Observer={}.already watch.", parentPath, childrenObserver);
            }
        }

        log.debug("A parent path[{}] watcher was created.", parentPath);
    }

    private final Map<TreeCacheParam, TreeCache> watchDataTreeCache = new ConcurrentHashMap<>();

    @Override
    @SneakyThrows
    public void watchData(String path, final Observer dataObserver) {
        //initZkClient();
        log.debug("watchData path={}", path);

        TreeCacheParam treeCacheParam = new TreeCacheParam(path, dataObserver);

        synchronized (watchDataTreeCache) {
            if (!watchDataTreeCache.containsKey(treeCacheParam)) {
                final Observable _this = this;
                TreeCache treeCache = new TreeCache(client, path);
                //设置监听器和处理过程
                treeCache.getListenable().addListener((client, event) -> {
                    ChildData data = event.getData();
                    if (data != null) {
                        switch (event.getType()) {
                            case NODE_ADDED:
                            case NODE_REMOVED:
                                if (log.isDebugEnabled()) {
                                    log.debug("NODE_{}= {} Data= {}", event.getType(), data.getPath(), new String(data.getData(), CHARSET_UTF8));
                                }
                                break;
                            case NODE_UPDATED:
                                String dataString = new String(data.getData(), CHARSET_UTF8).trim();
                                log.debug("NODE_{}= {} Data= {}", event.getType(), data.getPath(), dataString);
                                dataObserver.update(_this, dataString);
                                break;
                            default:
                                break;
                        }
                    } else if (event.getType() != TreeCacheEvent.Type.INITIALIZED) {
                        log.warn("data is null = {}", event.getType());
                    }
                });

                //开始监听
                treeCache.start();
                watchDataTreeCache.put(treeCacheParam, treeCache);
            } else {
                log.debug("The path={};Observer={}.already watch.", path, dataObserver);
            }
        }
    }

    private String readExternalizedNodeValue(String raw) {
        // 如果 raw 是 JSON 格式，不能转换，可能会破坏 JSON 结构
        if (raw.startsWith("{") || raw.startsWith("[")) {
            return raw;
        }
        return raw.replaceAll("\\\\n", "\n");
    }

    @Override
    @SneakyThrows
    public void importData(List<String> importFile, Boolean overwrite) {

        //initZkClient();
        log.debug("importData overwrite={}", overwrite);

        for (String line : importFile) {
            log.debug("Importing line:\t{}", line);
            if (!StringUtils.hasText(line)) {
                continue;
            }
            // Delete Operation
            if (line.startsWith("-")) {
                String nodeToDelete = line.substring(1);
                deleteNode(nodeToDelete);
            } else {
                int firstEq = line.indexOf('=');
                if (firstEq < 0) {
                    continue;
                }

                int secEq = line.indexOf('=', firstEq + 1);

                if (secEq < 0) {
                    continue;
                }

                String path = line.substring(0, firstEq);
                if ("/".equals(path)) {
                    path = "";
                }
                String name = line.substring(firstEq + 1, secEq);
                String value = readExternalizedNodeValue(line.substring(secEq + 1));
                String fullNodePath = path + "/" + name;

                // Skip import of system node
                if (fullNodePath.startsWith(ZK_SYSTEM_NODE)) {
                    log.debug("Skipping System Node Import: " + fullNodePath);
                    continue;
                }
                boolean nodeExists = exists(fullNodePath);

                if (!nodeExists) {
                    // If node doesnt exist then create it.
                    createPathAndNode(path, name, value.getBytes(CHARSET_UTF8), true);
                } else {
                    // If node exists then update only if overwrite flag is set.
                    if (overwrite) {
                        putNode(path, name, value);
                    } else {
                        log.info("Skipping update for existing property " + path + "/" + name + " as overwrite is not enabled!");
                    }
                }
            }
        }
    }

    @Override
    @SneakyThrows
    public Map<String, String> exportData(String path) {
        //initZkClient();
        log.debug("exportData path={}", path);

        Map<String, String> ret = new TreeMap<>();
        doExportDataRecursive(ret, path);
        return ret;
    }

    private void doExportDataRecursive(Map<String, String> map, String path) throws Exception {
        Map<String, String> childrenData = this.getChildrenWithData(path);
        if (childrenData != null && !childrenData.isEmpty()) {
            for (Map.Entry<String, String> entry : childrenData.entrySet()) {
                String childPath = entry.getKey();
                String childData = entry.getValue();
                File f = new File(childPath);
                String pathName = f.getName();
                if (!StringUtils.hasText(childData) || childData.equalsIgnoreCase(pathName)) {
                    doExportDataRecursive(map, childPath);
                } else {
                    map.put(String.format("%s=%s", path, pathName), removeLineBreak(childData));
                }
            }
        } else {
            int idx = path.lastIndexOf('/');
            String parent = path.substring(0, idx);
            String name = (idx < (path.length() - 1)) ? path.substring(idx + 1) : "";
            map.put(String.format("%s=%s", parent, name), "");
        }
    }

    private void createPathAndNode(String path, String name, byte[] data, boolean force) throws Exception {
        // 1. Create path nodes if necessary
        StringBuilder currPath = new StringBuilder();
        for (String folder : path.split("/")) {
            if (folder.isEmpty()) {
                continue;
            }
            currPath.append('/');
            currPath.append(folder);

            if (!exists(currPath.toString())) {
                this.putNode(currPath.toString(), new byte[0]);
            }
        }

        // 2. Create leaf node
        this.putNode(path + '/' + name, data);
    }

    @Override
    public void close() throws Exception {
        if (this.client != null) {
            log.debug("close");
            try {
                this.client.close();
            } catch (Throwable e) {
                log.error("client.close() error={}", e.getMessage());
            }
        }

        for (Map.Entry<TreeCacheParam, TreeCache> item : watchChildrenObserverTreeCache.entrySet()) {
            item.getValue().close();
        }
        for (Map.Entry<TreeCacheParam, TreeCache> item : watchDataTreeCache.entrySet()) {
            item.getValue().close();
        }
    }

    static class TreeCacheParam {
        private final String path;
        private final Observer observer;

        TreeCacheParam(String path, Observer observer) {
            this.path = path;
            this.observer = observer;
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            TreeCacheParam that = (TreeCacheParam) o;
            return Objects.equals(path, that.path) && Objects.equals(observer, that.observer);
        }

        @Override
        public int hashCode() {
            return Objects.hash(path, observer);
        }
    }
}
